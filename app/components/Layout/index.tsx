import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration, useLocation,
  useRouteLoaderData
} from '@remix-run/react';
import {
  PreventFlashOnWrongTheme,
  type Theme,
  ThemeProvider,
  useTheme
} from 'remix-themes';
import Header from '~/components/Header';
import Footer from '~/components/Footer';
import ScrollToTop from '~/components/ScrollToTop';
import { LoaderData } from '~/root';
import React from 'react';
import { QueryClient } from '@tanstack/query-core';
import { QueryClientProvider } from '@tanstack/react-query';
import {
  APP_ENV,
  BLOG_ASSETS_URL,
  CLOUDFLARE_ANALYTICS_TOKEN
} from "~/utils/env";
import { ENV, ROUTE_FOLDER, CLARITY_PROJECT_ID } from "~/constants/app";

export function Layout({ children }: { children: React.ReactNode }) {
  const data = useRouteLoaderData<LoaderData | { theme: Theme }>('root');
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        staleTime: 1000
      }
    }
  });

  if (typeof window !== 'undefined') {
    const theme = data?.theme || localStorage.getItem('theme') as Theme;
    if (theme) {
      localStorage.setItem('theme', theme);
    }
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        specifiedTheme={data?.theme as Theme}
        themeAction="set-theme-action"
      >
        <InnerLayout ssrTheme={Boolean(data?.theme)}>{children}</InnerLayout>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

function InnerLayout({
  ssrTheme,
  children
}: {
  ssrTheme: boolean;
  children: React.ReactNode;
}) {
  const [theme] = useTheme();
  const location = useLocation();

  const currentRoute = location.pathname.split('/')[2] || '';
  let isBlogDetail, isRouteFolder = false;

  ROUTE_FOLDER.forEach((route: string) => {
    if (route === currentRoute) {
      isRouteFolder = true;
    }
  });

  if (currentRoute !== '' && !isRouteFolder) {
    isBlogDetail = true;
  }

  return (
    <html lang="en" className={theme ?? ''}>
    <head>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <Meta /> <Links />

      {isBlogDetail && (
        <>
          <link media="all" type="text/css" rel="stylesheet"
                href={BLOG_ASSETS_URL + '/app/core/core/base/libraries/ckeditor/content-styles.css'} />
          <link media="all" type="text/css" rel="stylesheet"
                href={BLOG_ASSETS_URL + '/themes/ripple/css/style.css?v=7.4.6'} />
        </>
      )}
    </head>
    <body className="bg-white bg-gradient-to-r from-[#EDFBF9] to-[#FCF1F4] dark:bg-[#101926] dark:from-transparent dark:to-transparent relative"  suppressHydrationWarning>      <Header />
      {children}
      <Footer />
      <ScrollToTop />
      <ScrollRestoration />
      <PreventFlashOnWrongTheme ssrTheme={ssrTheme} />
      <Scripts />

      {APP_ENV === ENV.PROD && (
        <>
          <script
            defer
            src="https://static.cloudflareinsights.com/beacon.min.js"
            data-cf-beacon={`{"token": "${CLOUDFLARE_ANALYTICS_TOKEN}"}`}
          ></script>
          <script
            dangerouslySetInnerHTML={{
              __html: `
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "${CLARITY_PROJECT_ID}");
            `,
            }}
          />
        </>
      )}

    </body>
    </html>
  );
}

import { Button } from '@/components/ui/button';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslation } from 'react-i18next';
import LabelCustom from '@/components/Label';
import { NoData } from '@/components/NoData';
import { Input } from '@/components/ui/input';
import Modal from '@/components/Modal';
import { IAudienceDetail } from '@/types/audience';

type Props = {
  detail: IAudienceDetail;
};

const UpdateCustomAudience: React.FC<Props> = ({ detail }: Props) => {
  const { audience_name } = detail;
  const { items } = useSegmentContext();
  const { t } = useTranslation();

  const [payload, setPayload] = useState<{
    audience_name: string;
    segment_id: string;
  }>({ audience_name: '', segment_id: '' });

  const newOptions = items.reduce<{ label: string; value: string; count: number }[]>(
    (acc, item) => {
      if (item.contact_quantity > 0) {
        acc.push({
          label: item.name,
          value: item.id,
          count: item.contact_quantity,
        });
      }
      return acc;
    },
    [],
  );

  return (
    <Modal
      trigger={
        <Button variant={'ghost'} className="w-fit px-2 py-0 m-0 h-auto">
          {t('common.history')}
        </Button>
      }
      title={
        <div className="flex items-center flex-col">
          <span className="font-medium text-big360Color-neutral-950">
            {t('audience.addCustomAudience')}
          </span>
          <span className="text-sm font-normal text-big360Color-neutral-700">
            {t('audience.chooseSegment', { social: 'Tiktok' })}
          </span>
        </div>
      }
      titleAlign={'center'}
      className="max-w-[650px] w-full h-[534px]"
    >
      <div className="flex flex-col gap-4">
        <div className="flex flex-col">
          <LabelCustom className="mb-2" label={t('audience.customAudienceName')} />
          <Input
            value={audience_name}
            disabled
            className={cn('outline-none border h-10 w-full p-3 rounded-xl text-sm')}
            placeholder={t('segment.selectSegment')}
          />
        </div>
        <div className="h-[170px] rounded-xl bg-big360Color-neutral-50 border-big360Color-neutral-100 mb-2"></div>
        <div className="flex flex-col mb-2">
          <LabelCustom
            isRequire={true}
            className="mb-2"
            label={t('common.facebookAds.audiences.segment')}
          />
          <Select onValueChange={(value) => setPayload((prev) => ({ ...prev, segment_id: value }))}>
            <SelectTrigger className="w-full h-10 rounded-xl">
              <SelectValue placeholder={t('common.facebookAds.audiences.segmentPlaceholder')} />
            </SelectTrigger>
            <SelectContent className="max-h-[250px] overflow-auto p-2 rounded-xl">
              {!!newOptions.length ? (
                newOptions.map((item) => (
                  <SelectItem
                    className={cn(
                      'text-sm p-2 rounded-md cursor-pointer',
                      item.value === payload.segment_id &&
                        '!bg-brand text-white hover:text-white focus:text-white',
                    )}
                    key={item.value}
                    value={item.value}
                  >
                    <p>
                      {item.label}
                      <span className="text-xs text-big360Color-neutral-500">
                        {' - '}
                        {Number(item.count).toLocaleString()} {t('common.contacts')}
                      </span>
                    </p>
                  </SelectItem>
                ))
              ) : (
                <NoData />
              )}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-end justify-end gap-3">
          <Button
            onClick={() => {}}
            className="px-3 py-1 rounded-xl"
            variant={'secondary'}
            size={'lg'}
          >
            {t('common.button.cancel')}
          </Button>
          <Button onClick={() => {}} className="px-3 py-1 rounded-xl min-w-[144px]" size={'lg'}>
            {/*{loading ? <RiLoader2Line className="animate-spin" /> : t('tiktokAds.pushToTiktokAds')}*/}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
export default UpdateCustomAudience;
